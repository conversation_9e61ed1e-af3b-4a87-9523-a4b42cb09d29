#!/usr/bin/env python3
"""
Interactive Q&A with onlyspares.pdf using Mistral OCR.
"""

import sys
from pathlib import Path
from typing import List
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import settings
from src.agents.document_qa_agent import DocumentQAAgent
import fitz  # PyMuPDF


def setup_logging():
    """Setup basic logging."""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Convert PDF pages to images for OCR processing."""
    output_dir.mkdir(parents=True, exist_ok=True)
    image_paths = []
    
    logger.info(f"Converting PDF to images: {pdf_path}")
    
    # Open PDF
    pdf_doc = fitz.open(str(pdf_path))
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc[page_num]
        
        # Convert page to image (300 DPI for good OCR quality)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
        pix = page.get_pixmap(matrix=mat)
        
        # Save as PNG
        image_path = output_dir / f"page_{page_num + 1}.png"
        pix.save(str(image_path))
        image_paths.append(image_path)
        
        logger.info(f"Converted page {page_num + 1} to {image_path}")
    
    pdf_doc.close()
    return image_paths


def interactive_qa(qa_agent: DocumentQAAgent, image_paths: List[Path]):
    """Interactive Q&A session with the document."""
    print("\n" + "=" * 60)
    print("MISTRAL OCR DOCUMENT Q&A SESSION")
    print("=" * 60)
    print("Ask questions about the onlyspares.pdf document.")
    print("Type 'quit', 'exit', or 'q' to end the session.")
    print("Type 'help' for example questions.")
    print("=" * 60)
    
    example_questions = [
        "What spare parts are listed in this document?",
        "What are the part numbers mentioned?",
        "What is the price of [specific part]?",
        "How many items are in the spare parts list?",
        "What categories of spare parts are available?",
        "What is the description of part number [X]?",
        "Are there any warranty details mentioned?",
        "What supplier information is provided?"
    ]
    
    while True:
        try:
            question = input("\n🤖 Your question: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("\n👋 Ending Q&A session. Goodbye!")
                break
            
            if question.lower() == 'help':
                print("\n📝 Example questions you can ask:")
                for i, example in enumerate(example_questions, 1):
                    print(f"   {i}. {example}")
                continue
            
            if not question:
                continue
            
            print(f"\n🔍 Processing your question...")
            
            # Process question for each page
            answers_found = False
            
            for i, image_path in enumerate(image_paths):
                try:
                    answer = qa_agent.ocr_processor.extract_document_qa(image_path, question)
                    
                    # Only show answer if it seems relevant (not just "I cannot find...")
                    if answer and not any(phrase in answer.lower() for phrase in [
                        "cannot find", "not visible", "no information", "unable to", "not available"
                    ]):
                        print(f"\n📄 Answer from Page {i+1}:")
                        print("-" * 40)
                        print(answer)
                        print("-" * 40)
                        answers_found = True
                    
                except Exception as e:
                    logger.error(f"Error processing page {i+1}: {str(e)}")
                    continue
            
            if not answers_found:
                print("\n❌ No relevant information found for your question.")
                print("   Try rephrasing or asking about different aspects of the document.")
        
        except KeyboardInterrupt:
            print("\n\n👋 Q&A session interrupted. Goodbye!")
            break
        except Exception as e:
            logger.error(f"Error in Q&A session: {str(e)}")
            print(f"❌ Error processing your question: {str(e)}")


def main():
    """Main function for Q&A with onlyspares.pdf."""
    setup_logging()
    
    # File paths
    pdf_path = Path("example/onlyspares.pdf")
    temp_dir = Path("temp/onlyspares_qa")
    
    if not pdf_path.exists():
        logger.error(f"PDF file not found: {pdf_path}")
        print(f"❌ Error: PDF file not found at {pdf_path}")
        print("   Please make sure the file exists in the example/ directory.")
        return
    
    logger.info(f"Starting Q&A session with: {pdf_path}")
    
    try:
        # Initialize Document QA Agent
        logger.info("Initializing Mistral Document QA Agent...")
        qa_agent = DocumentQAAgent()
        
        # Convert PDF to images
        image_paths = pdf_to_images(pdf_path, temp_dir)
        
        # Start interactive Q&A
        interactive_qa(qa_agent, image_paths)
    
    except Exception as e:
        logger.error(f"Error initializing Q&A session: {str(e)}")
        print(f"❌ Error: {str(e)}")
        print("   Make sure your Mistral API key is configured correctly.")
    
    finally:
        # Clean up temporary files
        print("\n🧹 Cleaning up temporary files...")
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info("Temporary files cleaned up")


if __name__ == "__main__":
    main()
