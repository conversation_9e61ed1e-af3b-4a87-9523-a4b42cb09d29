#!/usr/bin/env python3
"""
Simple table extraction from onlyspares.pdf using Mistral OCR.
Extracts tables directly to CSV format.
"""

import sys
import json
import csv
from pathlib import Path
from typing import Dict, Any, List
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import settings
from src.processors.ocr_processor import MistralOCR
import fitz  # PyMuPDF


def setup_logging():
    """Setup basic logging."""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Convert PDF pages to images for OCR processing."""
    output_dir.mkdir(parents=True, exist_ok=True)
    image_paths = []
    
    logger.info(f"Converting PDF to images: {pdf_path}")
    
    # Open PDF
    pdf_doc = fitz.open(str(pdf_path))
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc[page_num]
        
        # Convert page to image (300 DPI for good OCR quality)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
        pix = page.get_pixmap(matrix=mat)
        
        # Save as PNG
        image_path = output_dir / f"page_{page_num + 1}.png"
        pix.save(str(image_path))
        image_paths.append(image_path)
        
        logger.info(f"Converted page {page_num + 1} to {image_path}")
    
    pdf_doc.close()
    return image_paths


def extract_tables_to_csv(structured_data: Dict[str, Any], output_dir: Path, page_num: int):
    """Extract tables from structured data and save to CSV."""
    tables = structured_data.get("tables", [])
    
    if not tables:
        logger.warning(f"No tables found on page {page_num}")
        return []
    
    logger.info(f"Found {len(tables)} table(s) on page {page_num}")
    
    csv_files = []
    for i, table in enumerate(tables):
        csv_filename = output_dir / f"onlyspares_page{page_num}_table{i+1}.csv"
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write headers if available
            headers = table.get("headers", [])
            if headers:
                writer.writerow(headers)
                logger.info(f"  Headers: {headers}")
            
            # Write rows
            rows = table.get("rows", [])
            for row in rows:
                writer.writerow(row)
            
            logger.info(f"  Rows: {len(rows)}")
        
        csv_files.append(csv_filename)
        logger.info(f"Table {i+1} saved to: {csv_filename}")
    
    return csv_files


def extract_all_text_to_file(structured_data: Dict[str, Any], output_dir: Path, page_num: int):
    """Extract all text and save to a text file."""
    text = structured_data.get("text", "")
    
    if not text.strip():
        logger.warning(f"No text found on page {page_num}")
        return None
    
    text_filename = output_dir / f"onlyspares_page{page_num}_text.txt"
    
    with open(text_filename, 'w', encoding='utf-8') as f:
        f.write(text)
    
    logger.info(f"Text from page {page_num} saved to: {text_filename}")
    return text_filename


def main():
    """Main function to extract tables from onlyspares.pdf."""
    setup_logging()
    
    # File paths
    pdf_path = Path("example/onlyspares.pdf")
    temp_dir = Path("temp/onlyspares_processing")
    output_dir = Path("outputs")
    
    if not pdf_path.exists():
        logger.error(f"PDF file not found: {pdf_path}")
        return
    
    logger.info(f"Processing PDF: {pdf_path}")
    
    try:
        # Initialize Mistral OCR
        logger.info("Initializing Mistral OCR...")
        ocr_processor = MistralOCR()
        
        # Convert PDF to images
        image_paths = pdf_to_images(pdf_path, temp_dir)
        
        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Process each page with OCR
        all_csv_files = []
        all_text_files = []
        
        for i, image_path in enumerate(image_paths):
            page_num = i + 1
            logger.info(f"Processing page {page_num} with Mistral OCR...")
            
            # Extract structured data
            structured_data = ocr_processor.extract_text_with_boxes(image_path)
            
            # Extract tables to CSV
            csv_files = extract_tables_to_csv(structured_data, output_dir, page_num)
            all_csv_files.extend(csv_files)
            
            # Extract all text to file
            text_file = extract_all_text_to_file(structured_data, output_dir, page_num)
            if text_file:
                all_text_files.append(text_file)
            
            # Save structured data as JSON for reference
            json_file = output_dir / f"onlyspares_page{page_num}_structured.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(structured_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Page {page_num} processed successfully")
        
        # Summary
        logger.info("=" * 50)
        logger.info("EXTRACTION COMPLETE!")
        logger.info(f"Processed {len(image_paths)} page(s)")
        logger.info(f"Extracted {len(all_csv_files)} table(s) to CSV")
        logger.info(f"Extracted {len(all_text_files)} text file(s)")
        
        if all_csv_files:
            logger.info("\nCSV Files Created:")
            for csv_file in all_csv_files:
                logger.info(f"  - {csv_file}")
        
        if all_text_files:
            logger.info("\nText Files Created:")
            for text_file in all_text_files:
                logger.info(f"  - {text_file}")
        
        logger.info(f"\nAll files saved to: {output_dir}")
        logger.info("=" * 50)
    
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        logger.exception("Full traceback:")
    
    finally:
        # Clean up temporary files
        cleanup = input("\nClean up temporary image files? (y/n): ").strip().lower()
        if cleanup in ['y', 'yes']:
            import shutil
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                logger.info("Temporary files cleaned up")


if __name__ == "__main__":
    main()
