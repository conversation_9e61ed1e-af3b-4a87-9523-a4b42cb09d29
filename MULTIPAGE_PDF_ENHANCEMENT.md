# Multi-Page PDF Enhancement for Mistral QA Integration

## Overview

The Mistral QA integration has been enhanced to properly handle multi-page PDF documents. Previously, only the first page of PDFs was processed, which limited the system's ability to extract comprehensive information from complex technical documents.

## New Features Implemented

### 1. **PDF Information Extraction**
- **`get_pdf_info()`**: Provides comprehensive PDF metadata including:
  - Total number of pages
  - Document title, author, subject
  - Preview of first 5 pages with text samples
- **Web endpoint**: `GET /qa/pdf-info/{file_id}` for retrieving PDF information

### 2. **Page-Specific Processing**
All QA methods now support page selection:
- **`answer_question(page_num=0)`**: Answer questions about specific pages
- **`extract_spare_parts_with_qa(page_num=None)`**: Extract spare parts from specific pages
- **`extract_maintenance_jobs_with_qa(page_num=0)`**: Extract maintenance info from specific pages
- **`analyze_document_structure(page_num=0)`**: Analyze structure of specific pages

### 3. **Multi-Page Question Answering**
- **`answer_question_multipage()`**: Searches through multiple relevant pages
- **Smart page detection**: Automatically finds pages likely to contain relevant content
- **Content-aware search**: Different strategies for spare parts vs. maintenance content
- **Best answer selection**: Returns the most comprehensive answer found

### 4. **Intelligent Page Discovery**
- **`_find_relevant_pages()`**: Uses keyword analysis to identify relevant pages
- **Content type detection**: Separate strategies for:
  - Spare parts (looks for "parts list", "pos. no", "qty", etc.)
  - Maintenance (looks for "maintenance", "inspection", "service", etc.)
  - Specifications (looks for "specifications", "technical data", etc.)

### 5. **Multi-Page Spare Parts Extraction**
- **`_extract_spare_parts_multipage()`**: Processes multiple pages automatically
- **Duplicate removal**: Eliminates duplicate parts based on part numbers
- **Comprehensive results**: Combines spare parts from all relevant pages
- **Progress tracking**: Reports which pages were processed

### 6. **Enhanced Web Interface**
- **PDF Information Display**: Shows total pages and page previews
- **Page Selection Controls**: Input field for specific page numbers
- **Multi-page Search Option**: Checkbox to enable comprehensive search
- **Dynamic Page Limits**: Automatically sets max page numbers based on PDF

## Technical Implementation

### Core Methods Enhanced

```python
# Page-specific question answering
result = qa_agent.answer_question(pdf_path, "What spare parts are listed?", page_num=5)

# Multi-page comprehensive search
result = qa_agent.answer_question_multipage(pdf_path, "What spare parts are listed?")

# Automatic multi-page spare parts extraction
result = qa_agent.extract_spare_parts_with_qa(pdf_path)  # Processes all relevant pages

# Specific page spare parts extraction
result = qa_agent.extract_spare_parts_with_qa(pdf_path, page_num=10)
```

### Web API Enhancements

```bash
# Get PDF information
GET /qa/pdf-info/{file_id}

# Ask question about specific page
POST /qa/question
  file_id: "abc123"
  question: "What is the part number for item 5?"
  page_num: 10
  multipage: false

# Multi-page question answering
POST /qa/question
  file_id: "abc123"
  question: "List all spare parts in this document"
  multipage: true

# Extract spare parts from specific page
POST /qa/extract-spare-parts
  file_id: "abc123"
  page_num: 15

# Extract spare parts from all relevant pages (default)
POST /qa/extract-spare-parts
  file_id: "abc123"
```

### Smart Page Detection Algorithm

The system uses keyword-based analysis to identify relevant pages:

1. **Spare Parts Detection**:
   - Keywords: "spare parts", "parts list", "pos. no", "qty", "part no", "description", "breakdown"
   - Threshold: Pages with 2+ keyword matches are considered relevant

2. **Maintenance Detection**:
   - Keywords: "maintenance", "inspection", "service", "hours", "months", "schedule", "interval"
   - Threshold: Pages with 2+ keyword matches are considered relevant

3. **Fallback Strategy**:
   - If no relevant pages found, processes first 3 pages
   - Limits processing to maximum 10 pages for performance

## Performance Considerations

### Processing Time
- **Single page**: ~3-5 seconds per page
- **Multi-page search**: ~15-30 seconds for 5-10 pages
- **Smart page detection**: Adds ~1-2 seconds for page analysis

### Memory Usage
- **PDF conversion**: ~2-5MB per page (300 DPI images)
- **Temporary files**: Automatically cleaned up after processing
- **API payload**: ~1MB base64 per page sent to Mistral

### Rate Limiting
- **Mistral API**: Respects rate limits with sequential processing
- **Concurrent requests**: Processes pages sequentially to avoid limits
- **Error handling**: Continues processing if individual pages fail

## Usage Examples

### Example 1: Tank Cleaning Machine PDF (62 pages)

```python
# Get PDF information
pdf_info = qa_agent.get_pdf_info(Path("TANK CLEANING MACHINE.pdf"))
print(f"Total pages: {pdf_info['total_pages']}")  # 62

# Find spare parts across all relevant pages
spare_parts = qa_agent.extract_spare_parts_with_qa(Path("TANK CLEANING MACHINE.pdf"))
print(f"Found {len(spare_parts['spare_parts'])} unique parts")

# Ask comprehensive question
result = qa_agent.answer_question_multipage(
    Path("TANK CLEANING MACHINE.pdf"),
    "What are all the different Scanjet models mentioned in this document?"
)
```

### Example 2: Specific Page Analysis

```python
# Analyze specific page that contains spare parts
result = qa_agent.extract_spare_parts_with_qa(
    Path("TANK CLEANING MACHINE.pdf"),
    page_num=25  # 26th page (0-based indexing)
)

# Ask question about specific page
answer = qa_agent.answer_question(
    Path("TANK CLEANING MACHINE.pdf"),
    "What is the drawing number on this page?",
    page_num=0  # First page
)
```

## Web Interface Usage

1. **Upload PDF**: Upload your multi-page PDF document
2. **View PDF Info**: Automatically displays total pages and previews
3. **Page Selection**: 
   - Use page number field (0-based: 0 = first page, 1 = second page, etc.)
   - Check "Search multiple pages" for comprehensive analysis
4. **Quick Actions**: 
   - Extract Spare Parts: Automatically finds and processes relevant pages
   - Extract Maintenance: Processes pages with maintenance information
   - Analyze Structure: Analyzes document structure page by page

## Benefits for Maritime Documents

### For Technical Manuals (like Tank Cleaning Machine)
- **Comprehensive Parts Lists**: Extracts spare parts from all relevant pages
- **Multiple Equipment Types**: Handles documents covering multiple Scanjet models
- **Cross-Reference Capability**: Finds related information across different pages

### For Maintenance Manuals
- **Complete Schedules**: Extracts maintenance information from entire document
- **Equipment-Specific Info**: Identifies maintenance for specific components
- **Interval Analysis**: Finds all maintenance intervals mentioned

### For Specification Documents
- **Technical Data**: Extracts specifications from multiple sections
- **Performance Metrics**: Gathers performance data across pages
- **Compliance Information**: Finds regulatory and compliance data

## Future Enhancements

1. **Batch Page Processing**: Process multiple specific pages simultaneously
2. **Page Range Selection**: Allow users to specify page ranges (e.g., pages 10-20)
3. **Content Caching**: Cache processed pages to avoid re-conversion
4. **Visual Page Selection**: Thumbnail view for page selection
5. **Cross-Page Analysis**: Detect relationships between information on different pages
6. **Export by Page**: Export results organized by source page

## Testing the Enhancement

To test the multi-page functionality:

1. **Upload a multi-page PDF** through the web interface
2. **Check PDF Info**: Verify that page count and previews are displayed
3. **Test Page Selection**: Try asking questions about different pages
4. **Test Multi-Page Search**: Enable multi-page search for comprehensive results
5. **Test Spare Parts Extraction**: Use the quick action to extract from all relevant pages

The system now provides comprehensive document understanding capabilities for complex multi-page technical documents, making it much more effective for maritime industry use cases.
