"""API routes for the web interface."""

import os
import uuid
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, Request, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from loguru import logger

from ..agents.processing_coordinator import ProcessingCoordinator
from ..agents.document_qa_agent import DocumentQAAgent
from ..exporters.excel_exporter import ExcelExporter
from ..exporters.template_manager import TemplateManager
from ..config import settings

router = APIRouter()
templates = Jinja2Templates(directory=str(Path(__file__).parent / "templates"))

# Global instances
processing_coordinator = ProcessingCoordinator()
excel_exporter = ExcelExporter()
template_manager = TemplateManager()

# Initialize QA agent (with error handling)
try:
    qa_agent = DocumentQAAgent()
    QA_AVAILABLE = True
except Exception as e:
    logger.warning(f"QA Agent not available: {str(e)}")
    qa_agent = None
    QA_AVAILABLE = False

# In-memory storage for processing status (use database in production)
processing_status = {}


@router.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page."""
    return templates.TemplateResponse("index.html", {"request": request})


@router.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """Upload page."""
    available_templates = template_manager.list_available_templates()
    return templates.TemplateResponse(
        "upload.html", 
        {"request": request, "templates": available_templates}
    )


@router.post("/upload")
async def upload_file(
    background_tasks: BackgroundTasks,
    request: Request,
    file: UploadFile = File(...),
    use_ocr: bool = Form(True),
    extract_entities: bool = Form(True),
    template_name: Optional[str] = Form(None)
):
    """Handle file upload and start processing."""
    try:
        # Validate file
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are allowed")
        
        if file.size > settings.max_file_size_mb * 1024 * 1024:
            raise HTTPException(
                status_code=400, 
                detail=f"File size exceeds {settings.max_file_size_mb}MB limit"
            )
        
        # Generate unique processing ID
        process_id = str(uuid.uuid4())
        
        # Save uploaded file
        file_path = settings.uploads_dir / f"{process_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Initialize processing status
        processing_status[process_id] = {
            "status": "queued",
            "filename": file.filename,
            "progress": 0,
            "message": "Processing queued",
            "result_path": None,
            "template_name": template_name
        }
        
        # Start background processing
        background_tasks.add_task(
            process_document_background,
            process_id,
            file_path,
            use_ocr,
            extract_entities,
            template_name
        )
        
        logger.info(f"File uploaded and processing started: {file.filename} (ID: {process_id})")
        
        return JSONResponse({
            "success": True,
            "process_id": process_id,
            "message": "File uploaded successfully. Processing started.",
            "redirect_url": f"/status/{process_id}"
        })
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{process_id}", response_class=HTMLResponse)
async def status_page(request: Request, process_id: str):
    """Processing status page."""
    if process_id not in processing_status:
        raise HTTPException(status_code=404, detail="Process not found")
    
    status_info = processing_status[process_id]
    return templates.TemplateResponse(
        "status.html",
        {"request": request, "process_id": process_id, "status": status_info}
    )


@router.get("/api/status/{process_id}")
async def get_status(process_id: str):
    """Get processing status via API."""
    if process_id not in processing_status:
        raise HTTPException(status_code=404, detail="Process not found")
    
    return JSONResponse(processing_status[process_id])


@router.get("/download/{process_id}")
async def download_result(process_id: str):
    """Download processing result."""
    if process_id not in processing_status:
        raise HTTPException(status_code=404, detail="Process not found")
    
    status_info = processing_status[process_id]
    
    if status_info["status"] != "completed" or not status_info["result_path"]:
        raise HTTPException(status_code=400, detail="Processing not completed or no result available")
    
    result_path = Path(status_info["result_path"])
    if not result_path.exists():
        raise HTTPException(status_code=404, detail="Result file not found")
    
    return FileResponse(
        path=str(result_path),
        filename=result_path.name,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )


@router.get("/templates", response_class=HTMLResponse)
async def templates_page(request: Request):
    """Templates management page."""
    available_templates = template_manager.list_available_templates()
    
    template_info = []
    for template_name in available_templates:
        template_path = template_manager.get_template_path(template_name)
        if template_path:
            structure = template_manager.analyze_template_structure(template_path)
            validation = template_manager.validate_template(template_path)
            
            template_info.append({
                "name": template_name,
                "filename": template_path.name,
                "sheets": len(structure.get("sheets", {})),
                "valid": validation.get("valid", False),
                "warnings": len(validation.get("warnings", [])),
                "errors": len(validation.get("errors", []))
            })
    
    return templates.TemplateResponse(
        "templates.html",
        {"request": request, "templates": template_info}
    )


@router.post("/templates/upload")
async def upload_template(file: UploadFile = File(...)):
    """Upload a custom template."""
    try:
        if not file.filename.lower().endswith('.xlsx'):
            raise HTTPException(status_code=400, detail="Only Excel files (.xlsx) are allowed")
        
        # Save template
        template_path = settings.templates_dir / file.filename
        with open(template_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Validate template
        validation = template_manager.validate_template(template_path)
        
        logger.info(f"Template uploaded: {file.filename}")
        
        return JSONResponse({
            "success": True,
            "message": "Template uploaded successfully",
            "validation": validation
        })
        
    except Exception as e:
        logger.error(f"Error uploading template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/templates/{template_name}")
async def get_template_info(template_name: str):
    """Get detailed template information."""
    template_path = template_manager.get_template_path(template_name)
    if not template_path:
        raise HTTPException(status_code=404, detail="Template not found")
    
    structure = template_manager.analyze_template_structure(template_path)
    validation = template_manager.validate_template(template_path)
    
    return JSONResponse({
        "name": template_name,
        "structure": structure,
        "validation": validation
    })


@router.delete("/api/processing/{process_id}")
async def cancel_processing(process_id: str):
    """Cancel processing."""
    if process_id not in processing_status:
        raise HTTPException(status_code=404, detail="Process not found")
    
    status_info = processing_status[process_id]
    
    if status_info["status"] in ["completed", "failed", "cancelled"]:
        raise HTTPException(status_code=400, detail="Cannot cancel completed/failed/cancelled process")
    
    # Update status
    processing_status[process_id]["status"] = "cancelled"
    processing_status[process_id]["message"] = "Processing cancelled by user"
    
    logger.info(f"Processing cancelled: {process_id}")
    
    return JSONResponse({"success": True, "message": "Processing cancelled"})


@router.get("/history", response_class=HTMLResponse)
async def history_page(request: Request):
    """Processing history page."""
    # Get recent processing history
    history = []
    for process_id, status_info in processing_status.items():
        history.append({
            "process_id": process_id,
            "filename": status_info["filename"],
            "status": status_info["status"],
            "message": status_info["message"],
            "has_result": status_info["result_path"] is not None
        })
    
    # Sort by most recent first (in production, use proper timestamp)
    history.reverse()
    
    return templates.TemplateResponse(
        "history.html",
        {"request": request, "history": history}
    )


async def process_document_background(
    process_id: str,
    file_path: Path,
    use_ocr: bool,
    extract_entities: bool,
    template_name: Optional[str]
):
    """Background task for document processing."""
    try:
        # Update status
        processing_status[process_id]["status"] = "processing"
        processing_status[process_id]["message"] = "Processing document..."
        processing_status[process_id]["progress"] = 10
        
        # Process document
        result = processing_coordinator.process_document(
            file_path,
            use_ocr=use_ocr,
            extract_entities=extract_entities
        )
        
        processing_status[process_id]["progress"] = 80
        processing_status[process_id]["message"] = "Generating Excel output..."
        
        # Export to Excel
        template_path = None
        if template_name:
            template_path = template_manager.get_template_path(template_name)
        
        output_path = excel_exporter.export_processing_result(result, template_path)
        
        # Update final status
        processing_status[process_id]["status"] = "completed"
        processing_status[process_id]["message"] = "Processing completed successfully"
        processing_status[process_id]["progress"] = 100
        processing_status[process_id]["result_path"] = str(output_path)
        
        logger.info(f"Document processing completed: {process_id}")
        
        # Cleanup uploaded file
        try:
            file_path.unlink()
        except:
            pass
        
    except Exception as e:
        logger.error(f"Error in background processing {process_id}: {str(e)}")
        
        processing_status[process_id]["status"] = "failed"
        processing_status[process_id]["message"] = f"Processing failed: {str(e)}"
        processing_status[process_id]["progress"] = 0
        
        # Cleanup uploaded file
        try:
            file_path.unlink()
        except:
            pass


# QA Routes
@router.get("/qa", response_class=HTMLResponse)
async def qa_page(request: Request):
    """Document QA page."""
    return templates.TemplateResponse(
        "qa.html",
        {"request": request, "qa_available": QA_AVAILABLE}
    )


@router.post("/qa/upload")
async def upload_qa_image(file: UploadFile = File(...)):
    """Upload image for QA analysis."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Validate file
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.pdf']
        if not any(file.filename.lower().endswith(ext) for ext in allowed_extensions):
            raise HTTPException(
                status_code=400,
                detail="Only image files (JPG, PNG, BMP, TIFF, WebP) and PDF files are allowed"
            )

        # Check file size (limit to 50MB for large PDFs)
        max_size = 50 * 1024 * 1024  # 50MB
        if hasattr(file, 'size') and file.size and file.size > max_size:
            raise HTTPException(
                status_code=400,
                detail="File size too large. Maximum size is 50MB."
            )

        # Generate unique ID
        file_id = str(uuid.uuid4())

        # Save uploaded file
        file_path = settings.temp_dir / f"{file_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        logger.info(f"QA image uploaded: {file.filename} (ID: {file_id})")

        return JSONResponse({
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "message": "File uploaded successfully"
        })

    except Exception as e:
        logger.error(f"Error uploading QA image: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/qa/pdf-info/{file_id}")
async def get_pdf_info(file_id: str):
    """Get information about an uploaded PDF."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Find the uploaded file
        file_path = None
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            file_path = temp_file
            break

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Get PDF info
        pdf_info = qa_agent.get_pdf_info(file_path)

        return JSONResponse(pdf_info)

    except Exception as e:
        logger.error(f"Error getting PDF info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/qa/question")
async def ask_question(
    file_id: str = Form(...),
    question: str = Form(...),
    page_num: int = Form(0),
    multipage: bool = Form(False)
):
    """Ask a question about an uploaded document."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Find the uploaded file
        file_path = None
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            file_path = temp_file
            break

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Answer the question
        if multipage:
            result = qa_agent.answer_question_multipage(file_path, question)
        else:
            result = qa_agent.answer_question(file_path, question, page_num)

        logger.info(f"QA question answered for file {file_id}: {question} (page: {page_num}, multipage: {multipage})")

        return JSONResponse(result)

    except Exception as e:
        logger.error(f"Error answering QA question: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/qa/extract-spare-parts")
async def extract_spare_parts_qa(
    file_id: str = Form(...),
    page_num: int = Form(None)
):
    """Extract spare parts using QA approach."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Find the uploaded file
        file_path = None
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            file_path = temp_file
            break

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Extract spare parts
        result = qa_agent.extract_spare_parts_with_qa(file_path, page_num)

        logger.info(f"Spare parts extracted via QA for file {file_id} (page: {page_num})")

        return JSONResponse(result)

    except Exception as e:
        logger.error(f"Error extracting spare parts via QA: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/qa/extract-maintenance")
async def extract_maintenance_qa(
    file_id: str = Form(...),
    page_num: int = Form(0)
):
    """Extract maintenance jobs using QA approach."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Find the uploaded file
        file_path = None
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            file_path = temp_file
            break

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Extract maintenance jobs
        result = qa_agent.extract_maintenance_jobs_with_qa(file_path, page_num)

        logger.info(f"Maintenance jobs extracted via QA for file {file_id} (page: {page_num})")

        return JSONResponse(result)

    except Exception as e:
        logger.error(f"Error extracting maintenance jobs via QA: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/qa/analyze-structure")
async def analyze_document_structure_qa(
    file_id: str = Form(...),
    page_num: int = Form(0)
):
    """Analyze document structure using QA approach."""
    try:
        if not QA_AVAILABLE:
            raise HTTPException(status_code=503, detail="QA service not available")

        # Find the uploaded file
        file_path = None
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            file_path = temp_file
            break

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Analyze document structure
        result = qa_agent.analyze_document_structure(file_path, page_num)

        logger.info(f"Document structure analyzed via QA for file {file_id} (page: {page_num})")

        return JSONResponse(result)

    except Exception as e:
        logger.error(f"Error analyzing document structure via QA: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/qa/file/{file_id}")
async def delete_qa_file(file_id: str):
    """Delete uploaded QA file."""
    try:
        # Find and delete the file
        deleted = False
        for temp_file in settings.temp_dir.glob(f"{file_id}_*"):
            temp_file.unlink()
            deleted = True
            break

        if not deleted:
            raise HTTPException(status_code=404, detail="File not found")

        logger.info(f"QA file deleted: {file_id}")

        return JSONResponse({"success": True, "message": "File deleted successfully"})

    except Exception as e:
        logger.error(f"Error deleting QA file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
