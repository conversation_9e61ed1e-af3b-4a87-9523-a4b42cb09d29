{% extends "base.html" %}

{% block title %}Document QA - PDF Understanding Tool{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Document Question & Answer</h1>
            <p class="lead">Upload a document image and ask questions about its content using AI-powered analysis.</p>
        </div>
    </div>

    {% if not qa_available %}
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading">QA Service Unavailable</h4>
                <p>The document QA service is currently not available. Please check that the Mistral API key is
                    configured properly.</p>
            </div>
        </div>
    </div>
    {% else %}

    <!-- File Upload Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Upload Document</h5>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="file" class="form-label">Select Document</label>
                            <input type="file" class="form-control" id="file" name="file"
                                accept=".jpg,.jpeg,.png,.bmp,.tiff,.webp,.pdf" required>
                            <div class="form-text">Supported formats: JPG, PNG, BMP, TIFF, WebP, PDF (max 50MB)</div>
                        </div>
                        <button type="submit" class="btn btn-primary">Upload Document</button>
                    </form>
                    <div id="uploadStatus" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Info Section -->
    <div class="row mb-4" id="pdfInfo" style="display: none;">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Document Information</h5>
                </div>
                <div class="card-body">
                    <div id="pdfInfoContent"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- QA Interface Section -->
    <div class="row mb-4" id="qaInterface" style="display: none;">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Ask Questions</h5>
                </div>
                <div class="card-body">
                    <form id="questionForm">
                        <div class="mb-3">
                            <label for="question" class="form-label">Your Question</label>
                            <textarea class="form-control" id="question" name="question" rows="3"
                                placeholder="Ask anything about the document content..." required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pageNum" class="form-label">Page Number (0 = first page)</label>
                                    <input type="number" class="form-control" id="pageNum" name="pageNum" value="0"
                                        min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="multipage" name="multipage">
                                        <label class="form-check-label" for="multipage">
                                            Search multiple pages (slower but more comprehensive)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">Ask Question</button>
                    </form>
                    <div id="questionStatus" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row mb-4" id="quickActions" style="display: none;">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Analysis</h5>
                </div>
                <div class="card-body">
                    <p>Use these quick actions to extract specific information from your document:</p>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="extractSparePartsBtn">
                            Extract Spare Parts
                        </button>
                        <button type="button" class="btn btn-outline-info" id="extractMaintenanceBtn">
                            Extract Maintenance Jobs
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="analyzeStructureBtn">
                            Analyze Document Structure
                        </button>
                    </div>
                    <div id="quickActionStatus" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row" id="resultsSection" style="display: none;">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Results</h5>
                </div>
                <div class="card-body">
                    <div id="results"></div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>

<script>
    let currentFileId = null;

    // Upload form handler
    document.getElementById('uploadForm').addEventListener('submit', async function (e) {
        e.preventDefault();

        const formData = new FormData();
        const fileInput = document.getElementById('file');
        formData.append('file', fileInput.files[0]);

        const statusDiv = document.getElementById('uploadStatus');
        statusDiv.innerHTML = '<div class="alert alert-info">Uploading...</div>';

        try {
            const response = await fetch('/qa/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                currentFileId = result.file_id;
                statusDiv.innerHTML = `<div class="alert alert-success">${result.message}</div>`;

                // Show QA interface
                document.getElementById('qaInterface').style.display = 'block';
                document.getElementById('quickActions').style.display = 'block';

                // Load PDF info if it's a PDF
                loadPdfInfo();
            } else {
                statusDiv.innerHTML = `<div class="alert alert-danger">Upload failed: ${result.message}</div>`;
            }
        } catch (error) {
            statusDiv.innerHTML = `<div class="alert alert-danger">Upload error: ${error.message}</div>`;
        }
    });

    // Question form handler
    document.getElementById('questionForm').addEventListener('submit', async function (e) {
        e.preventDefault();

        if (!currentFileId) {
            alert('Please upload a document first');
            return;
        }

        const formData = new FormData();
        formData.append('file_id', currentFileId);
        formData.append('question', document.getElementById('question').value);
        formData.append('page_num', document.getElementById('pageNum').value);
        formData.append('multipage', document.getElementById('multipage').checked);

        const statusDiv = document.getElementById('questionStatus');
        statusDiv.innerHTML = '<div class="alert alert-info">Processing question...</div>';

        try {
            const response = await fetch('/qa/question', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                displayResults('Question Answer', result.answer);
                statusDiv.innerHTML = '<div class="alert alert-success">Question answered successfully!</div>';
            } else {
                statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${result.error || 'Unknown error'}</div>`;
            }
        } catch (error) {
            statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        }
    });

    // Quick action handlers
    document.getElementById('extractSparePartsBtn').addEventListener('click', async function () {
        await performQuickAction('/qa/extract-spare-parts', 'Extracting spare parts...', 'Spare Parts');
    });

    document.getElementById('extractMaintenanceBtn').addEventListener('click', async function () {
        await performQuickAction('/qa/extract-maintenance', 'Extracting maintenance jobs...', 'Maintenance Jobs');
    });

    document.getElementById('analyzeStructureBtn').addEventListener('click', async function () {
        await performQuickAction('/qa/analyze-structure', 'Analyzing document structure...', 'Document Structure');
    });

    async function performQuickAction(endpoint, loadingMessage, resultTitle) {
        if (!currentFileId) {
            alert('Please upload a document first');
            return;
        }

        const statusDiv = document.getElementById('quickActionStatus');
        statusDiv.innerHTML = `<div class="alert alert-info">${loadingMessage}</div>`;

        const formData = new FormData();
        formData.append('file_id', currentFileId);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                displayResults(resultTitle, result);
                statusDiv.innerHTML = '<div class="alert alert-success">Analysis completed successfully!</div>';
            } else {
                statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${result.error || 'Unknown error'}</div>`;
            }
        } catch (error) {
            statusDiv.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        }
    }

    function displayResults(title, data) {
        const resultsDiv = document.getElementById('results');
        const resultsSection = document.getElementById('resultsSection');

        let content = `<h6>${title}</h6>`;

        if (typeof data === 'string') {
            content += `<div class="alert alert-light"><pre>${data}</pre></div>`;
        } else {
            content += `<div class="alert alert-light"><pre>${JSON.stringify(data, null, 2)}</pre></div>`;
        }

        resultsDiv.innerHTML = content;
        resultsSection.style.display = 'block';

        // Scroll to results
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Load PDF info
    async function loadPdfInfo() {
        if (!currentFileId) return;

        try {
            const response = await fetch(`/qa/pdf-info/${currentFileId}`);
            const pdfInfo = await response.json();

            if (pdfInfo.total_pages > 1) {
                // Show PDF info section
                document.getElementById('pdfInfo').style.display = 'block';

                let infoHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Total Pages:</strong> ${pdfInfo.total_pages}</p>
                            ${pdfInfo.title ? `<p><strong>Title:</strong> ${pdfInfo.title}</p>` : ''}
                            ${pdfInfo.author ? `<p><strong>Author:</strong> ${pdfInfo.author}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            <p><strong>Page Preview:</strong></p>
                            <div style="max-height: 200px; overflow-y: auto;">
                `;

                pdfInfo.pages_preview.forEach(page => {
                    infoHtml += `
                        <div class="mb-2">
                            <strong>Page ${page.page_number}:</strong><br>
                            <small class="text-muted">${page.text_preview.substring(0, 100)}...</small>
                        </div>
                    `;
                });

                infoHtml += `
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('pdfInfoContent').innerHTML = infoHtml;

                // Update page number input max value
                document.getElementById('pageNum').max = pdfInfo.total_pages - 1;
            }
        } catch (error) {
            console.log('Could not load PDF info:', error);
        }
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function () {
        if (currentFileId) {
            fetch(`/qa/file/${currentFileId}`, { method: 'DELETE' });
        }
    });
</script>
{% endblock %}