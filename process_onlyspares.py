#!/usr/bin/env python3
"""
Process onlyspares.pdf using Mistral OCR for table extraction and QA functionality.
"""

import sys
import json
import csv
from pathlib import Path
from typing import Dict, Any, List
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import settings
from src.processors.ocr_processor import MistralOCR
from src.agents.document_qa_agent import DocumentQAAgent
from src.processors.pdf_processor import PDFProcessor
import fitz  # PyMuPDF


def setup_logging():
    """Setup basic logging."""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Convert PDF pages to images for OCR processing."""
    output_dir.mkdir(parents=True, exist_ok=True)
    image_paths = []
    
    logger.info(f"Converting PDF to images: {pdf_path}")
    
    # Open PDF
    pdf_doc = fitz.open(str(pdf_path))
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc[page_num]
        
        # Convert page to image (300 DPI for good OCR quality)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
        pix = page.get_pixmap(matrix=mat)
        
        # Save as PNG
        image_path = output_dir / f"page_{page_num + 1}.png"
        pix.save(str(image_path))
        image_paths.append(image_path)
        
        logger.info(f"Converted page {page_num + 1} to {image_path}")
    
    pdf_doc.close()
    return image_paths


def extract_tables_to_csv(structured_data: Dict[str, Any], output_path: Path):
    """Extract tables from structured data and save to CSV."""
    tables = structured_data.get("tables", [])
    
    if not tables:
        logger.warning("No tables found in the document")
        return
    
    logger.info(f"Found {len(tables)} table(s)")
    
    for i, table in enumerate(tables):
        csv_filename = output_path.parent / f"{output_path.stem}_table_{i+1}.csv"
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write headers if available
            headers = table.get("headers", [])
            if headers:
                writer.writerow(headers)
            
            # Write rows
            rows = table.get("rows", [])
            for row in rows:
                writer.writerow(row)
        
        logger.info(f"Table {i+1} saved to: {csv_filename}")


def interactive_qa(qa_agent: DocumentQAAgent, image_paths: List[Path]):
    """Interactive Q&A session with the document."""
    logger.info("Starting interactive Q&A session")
    logger.info("Type 'quit' or 'exit' to end the session")
    logger.info("=" * 50)
    
    while True:
        try:
            question = input("\nEnter your question about the document: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                logger.info("Ending Q&A session")
                break
            
            if not question:
                continue
            
            logger.info(f"Processing question: {question}")
            
            # Process question for each page (in case document has multiple pages)
            for i, image_path in enumerate(image_paths):
                logger.info(f"Analyzing page {i+1}...")
                
                try:
                    answer = qa_agent.ocr_processor.extract_document_qa(image_path, question)
                    
                    print(f"\n--- Answer from Page {i+1} ---")
                    print(answer)
                    print("-" * 40)
                    
                except Exception as e:
                    logger.error(f"Error processing page {i+1}: {str(e)}")
                    continue
        
        except KeyboardInterrupt:
            logger.info("\nQ&A session interrupted by user")
            break
        except Exception as e:
            logger.error(f"Error in Q&A session: {str(e)}")


def main():
    """Main function to process onlyspares.pdf."""
    setup_logging()
    
    # File paths
    pdf_path = Path("example/onlyspares.pdf")
    temp_dir = Path("temp/onlyspares_processing")
    output_dir = Path("outputs")
    
    if not pdf_path.exists():
        logger.error(f"PDF file not found: {pdf_path}")
        return
    
    logger.info(f"Processing PDF: {pdf_path}")
    
    try:
        # Initialize processors
        logger.info("Initializing Mistral OCR...")
        ocr_processor = MistralOCR()
        
        logger.info("Initializing Document QA Agent...")
        qa_agent = DocumentQAAgent()
        
        # Convert PDF to images
        image_paths = pdf_to_images(pdf_path, temp_dir)
        
        # Process each page with OCR
        all_structured_data = []
        
        for i, image_path in enumerate(image_paths):
            logger.info(f"Processing page {i+1} with Mistral OCR...")
            
            # Extract structured data
            structured_data = ocr_processor.extract_text_with_boxes(image_path)
            structured_data["page_number"] = i + 1
            structured_data["image_path"] = str(image_path)
            
            all_structured_data.append(structured_data)
            
            logger.info(f"Page {i+1} processed successfully")
        
        # Save structured data to JSON
        output_dir.mkdir(parents=True, exist_ok=True)
        json_output = output_dir / f"{pdf_path.stem}_structured_data.json"
        
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(all_structured_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Structured data saved to: {json_output}")
        
        # Extract tables to CSV
        for i, structured_data in enumerate(all_structured_data):
            page_output = output_dir / f"{pdf_path.stem}_page_{i+1}"
            extract_tables_to_csv(structured_data, page_output)
        
        # Display summary
        total_tables = sum(len(data.get("tables", [])) for data in all_structured_data)
        logger.info(f"Processing complete! Found {total_tables} table(s) across {len(image_paths)} page(s)")
        
        # Ask user if they want to start Q&A session
        print("\n" + "=" * 50)
        print("PROCESSING COMPLETE!")
        print(f"- Structured data saved to: {json_output}")
        print(f"- Tables extracted to CSV files in: {output_dir}")
        print("=" * 50)
        
        start_qa = input("\nWould you like to start an interactive Q&A session? (y/n): ").strip().lower()
        
        if start_qa in ['y', 'yes']:
            interactive_qa(qa_agent, image_paths)
        else:
            logger.info("Skipping Q&A session")
    
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        logger.exception("Full traceback:")
    
    finally:
        # Clean up temporary files if desired
        cleanup = input("\nClean up temporary image files? (y/n): ").strip().lower()
        if cleanup in ['y', 'yes']:
            import shutil
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                logger.info("Temporary files cleaned up")


if __name__ == "__main__":
    main()
