#!/usr/bin/env python3
"""
Extract ONLY spare parts information from PDFs using Mistral OCR.
Filters out general content and focuses on spare parts data.
"""

import sys
import json
import csv
from pathlib import Path
from typing import Dict, Any, List, Optional
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import settings
from src.processors.ocr_processor import MistralOCR
import fitz  # PyMuPDF


def setup_logging():
    """Setup basic logging."""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Convert PDF pages to images for OCR processing."""
    output_dir.mkdir(parents=True, exist_ok=True)
    image_paths = []
    
    logger.info(f"Converting PDF to images: {pdf_path}")
    
    # Open PDF
    pdf_doc = fitz.open(str(pdf_path))
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc[page_num]
        
        # Convert page to image (300 DPI for good OCR quality)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
        pix = page.get_pixmap(matrix=mat)
        
        # Save as PNG
        image_path = output_dir / f"page_{page_num + 1}.png"
        pix.save(str(image_path))
        image_paths.append(image_path)
        
        logger.info(f"Converted page {page_num + 1} to {image_path}")
    
    pdf_doc.close()
    return image_paths


def extract_spare_parts_only(ocr_processor: MistralOCR, image_path: Path) -> Dict[str, Any]:
    """Extract only spare parts information from a document page."""
    try:
        # Encode image
        base64_image = ocr_processor._encode_image_to_base64(image_path)
        
        # Specialized prompt for spare parts extraction
        spare_parts_prompt = """
        Analyze this document page and extract ONLY spare parts information. Ignore general text, manuals, descriptions, or other content.

        Look for:
        - Spare parts lists or tables
        - Part numbers (like P/N, Part No, Item No, etc.)
        - Part descriptions/names
        - Quantities (Qty)
        - Prices or costs
        - Spare parts catalogs or inventories

        IMPORTANT: Return ONLY a valid JSON object with this exact structure. Do not include any other text, explanations, or markdown formatting:

        {
            "has_spare_parts": true,
            "spare_parts": [
                {
                    "part_number": "part number if available",
                    "description": "part description",
                    "quantity": "quantity if available",
                    "price": "price if available",
                    "unit": "unit if available (pcs, kg, etc.)",
                    "remarks": "any additional notes"
                }
            ],
            "spare_parts_sections": ["section titles that contain spare parts"],
            "page_contains_spare_parts": true
        }

        If no spare parts are found, return:
        {
            "has_spare_parts": false,
            "spare_parts": [],
            "spare_parts_sections": [],
            "page_contains_spare_parts": false
        }

        Only extract actual spare parts data, not general equipment descriptions or manual content.
        Return ONLY the JSON object, nothing else.
        """
        
        from mistralai.models import UserMessage, TextChunk, ImageURLChunk
        
        # Prepare message for spare parts extraction
        messages = [
            UserMessage(
                content=[
                    TextChunk(text=spare_parts_prompt),
                    ImageURLChunk(image_url=f"data:image/jpeg;base64,{base64_image}")
                ]
            )
        ]
        
        # Call Mistral API
        response = ocr_processor.client.chat.complete(
            model=ocr_processor.model,
            messages=messages,
            max_tokens=4000
        )
        
        try:
            raw_response = response.choices[0].message.content.strip()
            logger.info(f"Raw response from Mistral: {raw_response[:200]}...")

            # Try to extract JSON from the response (sometimes it's wrapped in markdown)
            if "```json" in raw_response:
                # Extract JSON from markdown code block
                json_start = raw_response.find("```json") + 7
                json_end = raw_response.find("```", json_start)
                json_content = raw_response[json_start:json_end].strip()
            elif raw_response.startswith("{") and raw_response.endswith("}"):
                json_content = raw_response
            else:
                # Try to find JSON-like content
                import re
                json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
                if json_match:
                    json_content = json_match.group()
                else:
                    raise json.JSONDecodeError("No JSON found", raw_response, 0)

            # Parse JSON response
            spare_parts_data = json.loads(json_content)

            # Validate the structure
            if not isinstance(spare_parts_data, dict):
                raise ValueError("Response is not a dictionary")

            # Ensure required fields exist
            spare_parts_data.setdefault("has_spare_parts", False)
            spare_parts_data.setdefault("spare_parts", [])
            spare_parts_data.setdefault("spare_parts_sections", [])
            spare_parts_data.setdefault("page_contains_spare_parts", False)

            return spare_parts_data

        except (json.JSONDecodeError, ValueError) as e:
            # Fallback: try to extract spare parts info from raw text
            logger.warning(f"Failed to parse JSON response for {image_path}: {str(e)}")
            logger.info("Attempting to extract spare parts from raw text...")

            raw_response = response.choices[0].message.content

            # Simple text analysis to detect spare parts
            spare_parts_keywords = [
                "part number", "p/n", "part no", "item no", "spare part", "spare",
                "quantity", "qty", "price", "cost", "catalog", "inventory"
            ]

            has_spare_parts = any(keyword in raw_response.lower() for keyword in spare_parts_keywords)

            return {
                "has_spare_parts": has_spare_parts,
                "spare_parts": [],
                "spare_parts_sections": [],
                "page_contains_spare_parts": has_spare_parts,
                "raw_response": raw_response,
                "parsing_error": str(e)
            }
    
    except Exception as e:
        logger.error(f"Error extracting spare parts from {image_path}: {str(e)}")
        return {
            "has_spare_parts": False,
            "spare_parts": [],
            "spare_parts_sections": [],
            "page_contains_spare_parts": False,
            "error": str(e)
        }


def save_spare_parts_to_csv(all_spare_parts: List[Dict], output_path: Path):
    """Save extracted spare parts to CSV file."""
    if not all_spare_parts:
        logger.warning("No spare parts found to save")
        return None
    
    csv_path = output_path.parent / f"{output_path.stem}_spare_parts.csv"
    
    # Define CSV headers
    headers = ["Page", "Part Number", "Description", "Quantity", "Price", "Unit", "Remarks"]
    
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)
        
        for page_data in all_spare_parts:
            page_num = page_data.get("page_number", "Unknown")
            spare_parts = page_data.get("spare_parts", [])
            
            for part in spare_parts:
                row = [
                    page_num,
                    part.get("part_number", ""),
                    part.get("description", ""),
                    part.get("quantity", ""),
                    part.get("price", ""),
                    part.get("unit", ""),
                    part.get("remarks", "")
                ]
                writer.writerow(row)
    
    logger.info(f"Spare parts saved to CSV: {csv_path}")
    return csv_path


def process_pdf_for_spare_parts(pdf_path: Path) -> Dict[str, Any]:
    """Process a single PDF file to extract only spare parts."""
    logger.info(f"Processing PDF for spare parts: {pdf_path.name}")
    
    # Setup paths
    temp_dir = Path("temp") / f"spare_parts_{pdf_path.stem}"
    output_dir = Path("outputs")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # Initialize Mistral OCR
        ocr_processor = MistralOCR()
        
        # Convert PDF to images
        image_paths = pdf_to_images(pdf_path, temp_dir)
        
        # Process each page for spare parts
        all_spare_parts_data = []
        total_spare_parts = 0
        
        for i, image_path in enumerate(image_paths):
            page_num = i + 1
            logger.info(f"Analyzing page {page_num} for spare parts...")
            
            # Extract spare parts from this page
            spare_parts_data = extract_spare_parts_only(ocr_processor, image_path)
            spare_parts_data["page_number"] = page_num
            spare_parts_data["image_path"] = str(image_path)
            
            # Only keep pages that have spare parts
            if spare_parts_data.get("has_spare_parts", False):
                all_spare_parts_data.append(spare_parts_data)
                parts_count = len(spare_parts_data.get("spare_parts", []))
                total_spare_parts += parts_count
                logger.info(f"  ✅ Found {parts_count} spare parts on page {page_num}")
            else:
                logger.info(f"  ❌ No spare parts found on page {page_num}")
        
        # Save results
        results = {
            "pdf_name": pdf_path.name,
            "total_pages": len(image_paths),
            "pages_with_spare_parts": len(all_spare_parts_data),
            "total_spare_parts_found": total_spare_parts,
            "spare_parts_data": all_spare_parts_data
        }
        
        # Save detailed JSON
        json_path = output_dir / f"{pdf_path.stem}_spare_parts_detailed.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Save CSV
        csv_path = save_spare_parts_to_csv(all_spare_parts_data, output_dir / pdf_path.stem)
        
        # Clean up temp files
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        
        return {
            "success": True,
            "results": results,
            "json_path": json_path,
            "csv_path": csv_path
        }
    
    except Exception as e:
        logger.error(f"Error processing {pdf_path}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "pdf_name": pdf_path.name
        }


def main():
    """Main function to extract spare parts from PDFs."""
    setup_logging()
    
    example_dir = Path("example")
    
    if not example_dir.exists():
        logger.error("Example directory not found")
        return
    
    # Find all PDF files
    pdf_files = list(example_dir.glob("*.pdf"))
    
    if not pdf_files:
        logger.error("No PDF files found in example directory")
        return
    
    logger.info(f"Found {len(pdf_files)} PDF files to process")
    
    # Process each PDF
    all_results = []
    
    for pdf_path in pdf_files:
        logger.info("=" * 60)
        logger.info(f"PROCESSING: {pdf_path.name}")
        logger.info("=" * 60)
        
        result = process_pdf_for_spare_parts(pdf_path)
        all_results.append(result)
        
        if result["success"]:
            stats = result["results"]
            logger.info(f"✅ SUCCESS: {pdf_path.name}")
            logger.info(f"   Pages processed: {stats['total_pages']}")
            logger.info(f"   Pages with spare parts: {stats['pages_with_spare_parts']}")
            logger.info(f"   Total spare parts found: {stats['total_spare_parts_found']}")
            if result.get("csv_path"):
                logger.info(f"   CSV saved: {result['csv_path']}")
        else:
            logger.error(f"❌ FAILED: {pdf_path.name} - {result['error']}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("PROCESSING SUMMARY")
    logger.info("=" * 60)
    
    successful = [r for r in all_results if r["success"]]
    failed = [r for r in all_results if not r["success"]]
    
    logger.info(f"Successfully processed: {len(successful)}/{len(all_results)} files")
    
    if successful:
        total_spare_parts = sum(r["results"]["total_spare_parts_found"] for r in successful)
        logger.info(f"Total spare parts extracted: {total_spare_parts}")
        logger.info("\nFiles with spare parts:")
        for result in successful:
            stats = result["results"]
            if stats["total_spare_parts_found"] > 0:
                logger.info(f"  📄 {stats['pdf_name']}: {stats['total_spare_parts_found']} spare parts")
    
    if failed:
        logger.info(f"\nFailed files: {len(failed)}")
        for result in failed:
            logger.info(f"  ❌ {result['pdf_name']}: {result['error']}")


if __name__ == "__main__":
    main()
