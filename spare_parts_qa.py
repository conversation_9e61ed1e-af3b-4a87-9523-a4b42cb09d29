#!/usr/bin/env python3
"""
Interactive Q&A focused on spare parts from PDFs using Mistral OCR.
"""

import sys
import os
from pathlib import Path
from typing import List
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import settings
from src.agents.document_qa_agent import DocumentQAAgent
import fitz  # PyMuPDF


def setup_logging():
    """Setup basic logging."""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Convert PDF pages to images for OCR processing."""
    output_dir.mkdir(parents=True, exist_ok=True)
    image_paths = []
    
    logger.info(f"Converting PDF to images: {pdf_path}")
    
    # Open PDF
    pdf_doc = fitz.open(str(pdf_path))
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc[page_num]
        
        # Convert page to image (300 DPI for good OCR quality)
        mat = fitz.Matrix(300/72, 300/72)  # 300 DPI scaling
        pix = page.get_pixmap(matrix=mat)
        
        # Save as PNG
        image_path = output_dir / f"page_{page_num + 1}.png"
        pix.save(str(image_path))
        image_paths.append(image_path)
        
        logger.info(f"Converted page {page_num + 1} to {image_path}")
    
    pdf_doc.close()
    return image_paths


def ask_spare_parts_question(qa_agent: DocumentQAAgent, image_paths: List[Path], question: str) -> str:
    """Ask a question specifically about spare parts."""
    
    # Enhanced prompt that focuses on spare parts
    spare_parts_focused_question = f"""
    Focus ONLY on spare parts information in this document. Ignore general equipment descriptions, manuals, or other content.
    
    Question: {question}
    
    When answering:
    - Only mention spare parts, part numbers, quantities, prices, or spare parts catalog information
    - Ignore general equipment operation, maintenance procedures, or technical specifications
    - If the question is about spare parts but no spare parts are visible, say "No spare parts information found for this question"
    - Be specific about part numbers, descriptions, and quantities when available
    """
    
    answers = []
    
    for i, image_path in enumerate(image_paths):
        try:
            answer = qa_agent.ocr_processor.extract_document_qa(image_path, spare_parts_focused_question)
            
            # Filter out non-spare-parts responses
            if answer and not any(phrase in answer.lower() for phrase in [
                "no spare parts information found",
                "cannot find spare parts",
                "not visible",
                "no information about spare parts"
            ]):
                # Check if answer actually contains spare parts related content
                spare_parts_keywords = [
                    "part number", "p/n", "part no", "item no", "spare part", "spare",
                    "quantity", "qty", "price", "cost", "catalog", "inventory"
                ]
                
                if any(keyword in answer.lower() for keyword in spare_parts_keywords):
                    answers.append(f"Page {i+1}: {answer}")
        
        except Exception as e:
            logger.error(f"Error processing page {i+1}: {str(e)}")
            continue
    
    if answers:
        return "\n\n".join(answers)
    else:
        return "No spare parts information found related to your question."


def select_pdf_file() -> Path:
    """Let user select which PDF file to analyze."""
    example_dir = Path("example")
    
    if not example_dir.exists():
        raise FileNotFoundError("Example directory not found")
    
    pdf_files = list(example_dir.glob("*.pdf"))
    
    if not pdf_files:
        raise FileNotFoundError("No PDF files found in example directory")
    
    print("\n📁 Available PDF files:")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"   {i}. {pdf_file.name}")
    
    while True:
        try:
            choice = input(f"\nSelect a PDF file (1-{len(pdf_files)}): ").strip()
            index = int(choice) - 1
            
            if 0 <= index < len(pdf_files):
                return pdf_files[index]
            else:
                print(f"Please enter a number between 1 and {len(pdf_files)}")
        
        except ValueError:
            print("Please enter a valid number")
        except KeyboardInterrupt:
            print("\nOperation cancelled")
            sys.exit(0)


def interactive_spare_parts_qa(qa_agent: DocumentQAAgent, image_paths: List[Path], pdf_name: str):
    """Interactive Q&A session focused on spare parts."""
    print("\n" + "=" * 70)
    print(f"SPARE PARTS Q&A SESSION - {pdf_name}")
    print("=" * 70)
    print("Ask questions specifically about SPARE PARTS in this document.")
    print("Type 'quit', 'exit', or 'q' to end the session.")
    print("Type 'help' for example spare parts questions.")
    print("=" * 70)
    
    example_questions = [
        "What spare parts are listed in this document?",
        "List all part numbers mentioned",
        "What is the quantity of [specific part]?",
        "What spare parts are needed for maintenance?",
        "Show me the spare parts catalog",
        "What is the price of part number [X]?",
        "How many different spare parts are available?",
        "What spare parts are recommended for [equipment name]?",
        "List spare parts by category",
        "What consumable spare parts are mentioned?"
    ]
    
    while True:
        try:
            question = input("\n🔧 Your spare parts question: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("\n👋 Ending spare parts Q&A session. Goodbye!")
                break
            
            if question.lower() == 'help':
                print("\n📝 Example spare parts questions:")
                for i, example in enumerate(example_questions, 1):
                    print(f"   {i}. {example}")
                continue
            
            if not question:
                continue
            
            print(f"\n🔍 Searching for spare parts information...")
            
            # Get answer focused on spare parts
            answer = ask_spare_parts_question(qa_agent, image_paths, question)
            
            print(f"\n📋 Spare Parts Answer:")
            print("-" * 50)
            print(answer)
            print("-" * 50)
        
        except KeyboardInterrupt:
            print("\n\n👋 Q&A session interrupted. Goodbye!")
            break
        except Exception as e:
            logger.error(f"Error in Q&A session: {str(e)}")
            print(f"❌ Error processing your question: {str(e)}")


def main():
    """Main function for spare parts Q&A."""
    setup_logging()
    
    try:
        # Select PDF file
        pdf_path = select_pdf_file()
        logger.info(f"Selected PDF: {pdf_path.name}")
        
        # Initialize Document QA Agent
        logger.info("Initializing Mistral Document QA Agent...")
        qa_agent = DocumentQAAgent()
        
        # Convert PDF to images
        temp_dir = Path("temp") / f"spare_parts_qa_{pdf_path.stem}"
        image_paths = pdf_to_images(pdf_path, temp_dir)
        
        # Start interactive Q&A focused on spare parts
        interactive_spare_parts_qa(qa_agent, image_paths, pdf_path.name)
    
    except Exception as e:
        logger.error(f"Error initializing spare parts Q&A: {str(e)}")
        print(f"❌ Error: {str(e)}")
        print("   Make sure your Mistral API key is configured correctly.")
    
    finally:
        # Clean up temporary files
        print("\n🧹 Cleaning up temporary files...")
        import shutil
        temp_dirs = Path("temp").glob("spare_parts_qa_*")
        for temp_dir in temp_dirs:
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up: {temp_dir}")


if __name__ == "__main__":
    main()
